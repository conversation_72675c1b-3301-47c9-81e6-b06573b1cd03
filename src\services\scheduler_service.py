"""
Daily Reminder Scheduler Service
Handles scheduling and execution of daily bonus reminder notifications
"""

import asyncio
import logging
from datetime import datetime, time, timedelta
from typing import Optional, Dict, Any
import pytz
from motor.motor_asyncio import AsyncIOMotorDatabase
from telegram import Bo<PERSON>

from config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class SchedulerService:
    """Service for scheduling daily reminder notifications"""
    
    def __init__(self, db: AsyncIOMotorDatabase, bot: Bot, notification_service):
        self.db = db
        self.bot = bot
        self.notification_service = notification_service
        
        # IST timezone for scheduling
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        # Scheduler settings
        self.reminder_time = time(10, 0)  # 10:00 AM IST
        self.scheduler_running = False
        self.scheduler_task = None
        
        # Performance settings
        self.check_interval = 60  # Check every minute for scheduled tasks
    
    async def start_scheduler(self):
        """Start the daily reminder scheduler"""
        try:
            if self.scheduler_running:
                logger.warning("Scheduler is already running")
                return False
            
            self.scheduler_running = True
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            
            logger.info(f"🕐 Daily reminder scheduler started - reminders at {self.reminder_time.strftime('%H:%M')} IST")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            self.scheduler_running = False
            return False
    
    async def stop_scheduler(self):
        """Stop the daily reminder scheduler"""
        try:
            if not self.scheduler_running:
                logger.warning("Scheduler is not running")
                return False
            
            self.scheduler_running = False
            
            if self.scheduler_task:
                self.scheduler_task.cancel()
                try:
                    await self.scheduler_task
                except asyncio.CancelledError:
                    pass
                self.scheduler_task = None
            
            logger.info("🛑 Daily reminder scheduler stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping scheduler: {e}")
            return False
    
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        try:
            logger.info("📅 Scheduler loop started")
            
            while self.scheduler_running:
                try:
                    # Check if it's time to send daily reminders
                    if await self._should_send_reminders():
                        logger.info("⏰ Time to send daily reminders!")
                        await self._execute_daily_reminders()
                    
                    # Wait before next check
                    await asyncio.sleep(self.check_interval)
                    
                except asyncio.CancelledError:
                    logger.info("Scheduler loop cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in scheduler loop: {e}")
                    # Continue running even if there's an error
                    await asyncio.sleep(self.check_interval)
            
            logger.info("📅 Scheduler loop ended")
            
        except Exception as e:
            logger.error(f"Fatal error in scheduler loop: {e}")
            self.scheduler_running = False
    
    async def _should_send_reminders(self) -> bool:
        """Check if it's time to send daily reminders"""
        try:
            # Get current time in IST
            ist_now = datetime.now(self.ist_tz)
            current_time = ist_now.time()
            
            # Check if current time is within 1 minute of reminder time
            reminder_datetime = datetime.combine(ist_now.date(), self.reminder_time)
            reminder_datetime = self.ist_tz.localize(reminder_datetime)
            
            time_diff = abs((ist_now - reminder_datetime).total_seconds())
            
            # Send reminders if within 1 minute of scheduled time
            if time_diff <= 60:
                # Check if we already sent reminders today
                today_start = self.ist_tz.localize(
                    datetime.combine(ist_now.date(), time.min)
                )
                
                # Check if we have a log entry for today's broadcast
                today_broadcast = await self.db.notification_logs.find_one({
                    'type': 'daily_reminder_broadcast',
                    'ist_timestamp': {
                        '$gte': today_start.isoformat(),
                        '$lt': (today_start + timedelta(days=1)).isoformat()
                    }
                })
                
                if not today_broadcast:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking reminder schedule: {e}")
            return False
    
    async def _execute_daily_reminders(self):
        """Execute daily reminder broadcast with automatic method selection based on user count"""
        try:
            logger.info("🚀 Executing daily reminder broadcast...")

            # Get user count to determine optimal method
            user_count = await self.notification_service.users_collection.count_documents({
                'daily_notifications': True,
                'is_active': True,
                'is_banned': False,
                'has_joined_channels': True
            })

            logger.info(f"📊 Found {user_count} users eligible for notifications")

            # Choose method based on user count for optimal performance
            if user_count > 500000:  # 500K+ users - use streaming approach
                logger.info("🌊 Using streaming approach for large dataset (500K+ users)")
                result = await self.notification_service.send_daily_reminders_streaming()
            else:  # <500K users - use standard batch approach
                logger.info("📦 Using standard batch approach for dataset")
                result = await self.notification_service.send_daily_reminders_to_all()

            if result['success']:
                logger.info(f"✅ Daily reminders sent successfully!")
                logger.info(f"📊 Stats: {result['sent_count']} sent, {result['failed_count']} failed, {result['cleanup_count']} cleaned up")
                logger.info(f"⏱️ Completed in {result['duration_seconds']:.2f} seconds")
            else:
                logger.error(f"❌ Daily reminder broadcast failed: {result.get('error', 'Unknown error')}")

            return result

        except Exception as e:
            logger.error(f"Error executing daily reminders: {e}")
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0
            }
    
    async def send_test_reminder(self, user_id: int) -> Dict[str, Any]:
        """Send a test reminder to a specific user (for testing purposes)"""
        try:
            logger.info(f"Sending test reminder to user {user_id}")
            result = await self.notification_service.send_daily_reminder(user_id)
            return result
            
        except Exception as e:
            logger.error(f"Error sending test reminder to user {user_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def force_daily_broadcast(self) -> Dict[str, Any]:
        """Force immediate daily reminder broadcast (for admin testing)"""
        try:
            logger.info("🔧 Force executing daily reminder broadcast...")
            result = await self._execute_daily_reminders()
            return result
            
        except Exception as e:
            logger.error(f"Error in forced daily broadcast: {e}")
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': 0,
                'cleanup_count': 0
            }
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        try:
            ist_now = datetime.now(self.ist_tz)
            
            # Calculate next reminder time
            next_reminder = datetime.combine(ist_now.date(), self.reminder_time)
            next_reminder = self.ist_tz.localize(next_reminder)
            
            # If today's reminder time has passed, schedule for tomorrow
            if ist_now.time() > self.reminder_time:
                next_reminder += timedelta(days=1)
            
            return {
                'running': self.scheduler_running,
                'reminder_time_ist': self.reminder_time.strftime('%H:%M'),
                'current_time_ist': ist_now.strftime('%Y-%m-%d %H:%M:%S'),
                'next_reminder_ist': next_reminder.strftime('%Y-%m-%d %H:%M:%S'),
                'check_interval_seconds': self.check_interval
            }
            
        except Exception as e:
            logger.error(f"Error getting scheduler status: {e}")
            return {
                'running': False,
                'error': str(e)
            }
    
    async def update_reminder_time(self, hour: int, minute: int = 0) -> bool:
        """Update the daily reminder time"""
        try:
            if not (0 <= hour <= 23) or not (0 <= minute <= 59):
                logger.error(f"Invalid time: {hour}:{minute}")
                return False
            
            self.reminder_time = time(hour, minute)
            logger.info(f"📅 Reminder time updated to {self.reminder_time.strftime('%H:%M')} IST")
            return True
            
        except Exception as e:
            logger.error(f"Error updating reminder time: {e}")
            return False
