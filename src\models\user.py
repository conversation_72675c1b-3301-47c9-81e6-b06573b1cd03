"""
User model for the Telegram Referral Bot
"""

from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
import secrets
import string
import pytz

@dataclass
class User:
    """User model representing a bot user"""
    
    user_id: int
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    language_code: Optional[str] = None
    is_bot: bool = False
    is_premium: bool = False
    
    # Bot-specific fields
    balance: float = 0.0
    referral_code: Optional[str] = None
    referred_by: Optional[int] = None
    referral_count: int = 0
    
    # Status and settings
    is_active: bool = True
    is_banned: bool = False
    ban_reason: Optional[str] = None
    registration_completed: bool = False
    daily_notifications: bool = True  # Daily bonus reminder notifications
    
    # Subscription status
    has_joined_channels: bool = False
    joined_channels: List[str] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    last_activity: datetime = None
    last_daily_bonus: Optional[datetime] = None
    
    # Statistics
    total_referrals: int = 0
    successful_referrals: int = 0
    total_withdrawals: float = 0.0
    withdrawal_count: int = 0
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.joined_channels is None:
            self.joined_channels = []
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.last_activity is None:
            self.last_activity = datetime.now(timezone.utc)
        
        if self.referral_code is None:
            self.referral_code = self.generate_referral_code()
    
    @staticmethod
    def generate_referral_code(length: int = 8) -> str:
        """Generate a unique referral code"""
        characters = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def add_balance(self, amount: float, reason: str = ""):
        """Add amount to user balance (converted to integer)"""
        self.balance += int(round(amount))
        self.update_activity()

    def deduct_balance(self, amount: float) -> bool:
        """Deduct amount from user balance if sufficient funds (converted to integer)"""
        amount_int = int(round(amount))
        if self.balance >= amount_int:
            self.balance -= amount_int
            self.update_activity()
            return True
        return False
    
    def can_claim_daily_bonus(self) -> bool:
        """Check if user can claim daily bonus (resets daily at 12:00 AM IST)"""
        if not self.last_daily_bonus:
            return True

        # Get current time in IST (GMT+5:30)
        ist_tz = pytz.timezone('Asia/Kolkata')
        now_ist = datetime.now(ist_tz)

        # Convert last claim time to IST
        last_claim_utc = self.last_daily_bonus.replace(tzinfo=timezone.utc)
        last_claim_ist = last_claim_utc.astimezone(ist_tz)

        # Check if it's a different day in IST
        return now_ist.date() > last_claim_ist.date()
    
    def get_next_daily_bonus_time_ist(self) -> Optional[datetime]:
        """Get the next daily bonus claim time in IST"""
        if not self.last_daily_bonus:
            return None

        # Get IST timezone
        ist_tz = pytz.timezone('Asia/Kolkata')

        # Convert last claim time to IST
        last_claim_utc = self.last_daily_bonus.replace(tzinfo=timezone.utc)
        last_claim_ist = last_claim_utc.astimezone(ist_tz)

        # Next claim is at 12:00 AM IST the next day
        next_claim_date = last_claim_ist.date() + timedelta(days=1)
        next_claim_ist = ist_tz.localize(datetime.combine(next_claim_date, datetime.min.time()))

        return next_claim_ist

    def claim_daily_bonus(self, amount: int):
        """Claim daily bonus (integer amount)"""
        self.add_balance(amount, "Daily Bonus")
        self.last_daily_bonus = datetime.now(timezone.utc)

    def complete_registration(self):
        """Mark user registration as completed"""
        self.registration_completed = True
        self.update_activity()
    
    def add_referral(self):
        """Increment referral count"""
        self.referral_count += 1
        self.successful_referrals += 1
        self.update_activity()
    
    def ban_user(self, reason: str = ""):
        """Ban the user"""
        self.is_banned = True
        self.is_active = False
        self.ban_reason = reason
        self.update_activity()
    
    def unban_user(self):
        """Unban the user"""
        self.is_banned = False
        self.is_active = True
        self.ban_reason = None
        self.update_activity()
    
    def join_channel(self, channel_id: str):
        """Mark channel as joined"""
        if channel_id not in self.joined_channels:
            self.joined_channels.append(channel_id)
            self.update_activity()
    
    def leave_channel(self, channel_id: str):
        """Mark channel as left"""
        if channel_id in self.joined_channels:
            self.joined_channels.remove(channel_id)
            self.update_activity()
    
    def get_display_name(self) -> str:
        """Get user's display name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User {self.user_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user object to dictionary"""
        data = asdict(self)
        
        # Convert datetime objects to ISO format
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user object from dictionary"""
        # Create a copy of data to avoid modifying the original
        user_data = data.copy()

        # Remove MongoDB's _id field if present
        user_data.pop('_id', None)

        # Handle field name compatibility - convert 'banned' to 'is_banned' if present
        if 'banned' in user_data and 'is_banned' not in user_data:
            user_data['is_banned'] = user_data.pop('banned')

        # Handle other potential field name mismatches
        if 'referrer_id' in user_data and 'referred_by' not in user_data:
            user_data['referred_by'] = user_data.pop('referrer_id')

        # Remove any unknown fields that might cause initialization errors
        valid_fields = {
            'user_id', 'username', 'first_name', 'last_name', 'language_code', 'is_bot', 'is_premium',
            'balance', 'referral_code', 'referred_by', 'referral_count',
            'is_active', 'is_banned', 'ban_reason', 'registration_completed', 'daily_notifications', 'has_joined_channels', 'joined_channels',
            'created_at', 'updated_at', 'last_activity', 'last_daily_bonus',
            'total_referrals', 'successful_referrals', 'total_withdrawals', 'withdrawal_count'
        }

        # Filter out any fields not in the User dataclass
        filtered_data = {k: v for k, v in user_data.items() if k in valid_fields}

        # Convert various datetime formats back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'last_activity', 'last_daily_bonus']

        for field in datetime_fields:
            if field in filtered_data and filtered_data[field]:
                if isinstance(filtered_data[field], str):
                    # Handle ISO format strings
                    try:
                        filtered_data[field] = datetime.fromisoformat(filtered_data[field].replace('Z', '+00:00'))
                    except (ValueError, TypeError):
                        # Invalid string format, set to None
                        filtered_data[field] = None
                elif isinstance(filtered_data[field], (int, float)):
                    # Handle timestamp integers/floats
                    try:
                        filtered_data[field] = datetime.fromtimestamp(filtered_data[field], tz=timezone.utc)
                    except (ValueError, TypeError, OSError):
                        # Invalid timestamp, set to None
                        filtered_data[field] = None
                elif not isinstance(filtered_data[field], datetime):
                    # If it's not a datetime object, string, or number, set to None
                    filtered_data[field] = None

        return cls(**filtered_data)
    
    def __str__(self) -> str:
        """String representation of user"""
        return f"User(id={self.user_id}, name={self.get_display_name()}, balance={self.balance})"
